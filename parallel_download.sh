#!/bin/bash

# 并行下载Higgs Audio模型文件
echo "🚀 并行下载Higgs Audio模型文件"
echo "=" * 50

# 创建目录
mkdir -p models/higgs-audio-v2-generation-3B-base
mkdir -p models/higgs-audio-v2-tokenizer

# 基础URL
BASE_URL="https://hf-mirror.com"
MODEL_REPO="bosonai/higgs-audio-v2-generation-3B-base"
TOKENIZER_REPO="bosonai/higgs-audio-v2-tokenizer"

# 定义下载函数
download_file() {
    local url=$1
    local output_path=$2
    local filename=$(basename "$output_path")
    
    if [ -f "$output_path" ]; then
        echo "⏭️  文件已存在，跳过: $filename"
        return 0
    fi
    
    echo "📥 开始下载: $filename"
    if wget -c --timeout=30 --tries=3 "$url" -O "$output_path" 2>/dev/null; then
        echo "✅ 下载完成: $filename"
        return 0
    else
        echo "❌ 下载失败: $filename"
        return 1
    fi
}

# 导出函数以便在子shell中使用
export -f download_file
export BASE_URL MODEL_REPO TOKENIZER_REPO

echo "📥 开始并行下载主模型文件..."

# 主模型文件列表
model_files=(
    "model-00003-of-00003.safetensors"
    "tokenizer.json"
    "tokenizer_config.json"
    "special_tokens_map.json"
)

# 并行下载主模型文件
for file in "${model_files[@]}"; do
    {
        download_file "${BASE_URL}/${MODEL_REPO}/resolve/main/${file}" "models/higgs-audio-v2-generation-3B-base/${file}"
    } &
done

echo "📥 开始并行下载分词器文件..."

# 分词器文件列表
tokenizer_files=(
    "config.json"
    "pytorch_model.bin"
    "tokenizer_config.json"
)

# 并行下载分词器文件
for file in "${tokenizer_files[@]}"; do
    {
        download_file "${BASE_URL}/${TOKENIZER_REPO}/resolve/main/${file}" "models/higgs-audio-v2-tokenizer/${file}"
    } &
done

# 等待所有后台任务完成
echo "⏳ 等待所有下载任务完成..."
wait

echo ""
echo "🎉 并行下载完成！"
echo "📁 模型文件位置:"
echo "   - 主模型: models/higgs-audio-v2-generation-3B-base/"
echo "   - 分词器: models/higgs-audio-v2-tokenizer/"

# 检查下载结果
echo ""
echo "📊 下载结果检查:"
echo "主模型文件:"
for file in "${model_files[@]}"; do
    if [ -f "models/higgs-audio-v2-generation-3B-base/${file}" ]; then
        size=$(du -h "models/higgs-audio-v2-generation-3B-base/${file}" | cut -f1)
        echo "   ✅ $file ($size)"
    else
        echo "   ❌ $file (缺失)"
    fi
done

echo "分词器文件:"
for file in "${tokenizer_files[@]}"; do
    if [ -f "models/higgs-audio-v2-tokenizer/${file}" ]; then
        size=$(du -h "models/higgs-audio-v2-tokenizer/${file}" | cut -f1)
        echo "   ✅ $file ($size)"
    else
        echo "   ❌ $file (缺失)"
    fi
done

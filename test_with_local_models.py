#!/usr/bin/env python3
"""使用本地模型进行语音合成测试"""

import os
import time
import torch
import torchaudio
from pathlib import Path
from boson_multimodal.serve.serve_engine import HiggsAudioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message, AudioContent

# 本地模型路径
LOCAL_MODEL_PATH = "./models/higgs-audio-v2-generation-3B-base"
LOCAL_TOKENIZER_PATH = "./models/higgs-audio-v2-tokenizer"

def check_local_models():
    """检查本地模型文件是否存在"""
    model_path = Path(LOCAL_MODEL_PATH)
    tokenizer_path = Path(LOCAL_TOKENIZER_PATH)
    
    print("🔍 检查本地模型文件...")
    
    # 检查主模型文件
    required_model_files = [
        "config.json",
        "generation_config.json",
        "model.safetensors.index.json",
        "model-00001-of-00003.safetensors",
        "model-00002-of-00003.safetensors", 
        "model-00003-of-00003.safetensors",
        "tokenizer.json",
        "tokenizer_config.json",
        "special_tokens_map.json"
    ]
    
    missing_model_files = []
    for file in required_model_files:
        if not (model_path / file).exists():
            missing_model_files.append(file)
    
    # 检查分词器文件
    required_tokenizer_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer_config.json"
    ]
    
    missing_tokenizer_files = []
    for file in required_tokenizer_files:
        if not (tokenizer_path / file).exists():
            missing_tokenizer_files.append(file)
    
    if missing_model_files:
        print(f"❌ 主模型缺少文件: {missing_model_files}")
    else:
        print("✅ 主模型文件完整")
    
    if missing_tokenizer_files:
        print(f"❌ 分词器缺少文件: {missing_tokenizer_files}")
    else:
        print("✅ 分词器文件完整")
    
    return len(missing_model_files) == 0 and len(missing_tokenizer_files) == 0

def check_gpu():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        print(f"✅ CUDA可用，GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        return "cuda"
    elif torch.backends.mps.is_available():
        print("✅ MPS (Apple Silicon)可用")
        return "mps"
    else:
        print("⚠️  只有CPU可用，生成速度会较慢")
        return "cpu"

def test_basic_tts_local(serve_engine, output_dir):
    """使用本地模型测试基础TTS"""
    print("\n🎯 测试: 使用本地模型进行基础TTS")
    
    system_prompt = (
        "Generate audio following instruction.\n\n<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
    )
    
    messages = [
        Message(role="system", content=system_prompt),
        Message(role="user", content="Hello, this is a test using local Higgs Audio models. The weather is beautiful today!")
    ]
    
    start_time = time.time()
    try:
        output = serve_engine.generate(
            chat_ml_sample=ChatMLSample(messages=messages),
            max_new_tokens=1024,
            temperature=0.3,
            top_p=0.95,
            top_k=50,
            stop_strings=["<|end_of_text|>", "<|eot_id|>"],
        )
        end_time = time.time()
        
        output_path = output_dir / "local_model_test.wav"
        torchaudio.save(str(output_path), torch.from_numpy(output.audio)[None, :], output.sampling_rate)
        
        print(f"✅ 本地模型TTS测试成功")
        print(f"   生成时间: {end_time - start_time:.2f}秒")
        print(f"   输出文件: {output_path}")
        print(f"   音频长度: {len(output.audio) / output.sampling_rate:.2f}秒")
        return True
        
    except Exception as e:
        print(f"❌ 本地模型TTS测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 使用本地模型进行语音合成测试")
    print("=" * 50)
    
    # 检查本地模型文件
    if not check_local_models():
        print("\n❌ 本地模型文件不完整，请先下载完整的模型文件")
        print("运行以下命令下载模型:")
        print("   ./download_remaining_models.sh")
        return
    
    # 检查GPU
    device = check_gpu()
    
    # 创建输出目录
    output_dir = Path("local_model_outputs")
    output_dir.mkdir(exist_ok=True)
    print(f"📁 输出目录: {output_dir.absolute()}")
    
    # 初始化服务引擎
    print(f"\n🔧 初始化Higgs Audio服务引擎...")
    print(f"   模型路径: {LOCAL_MODEL_PATH}")
    print(f"   音频分词器: {LOCAL_TOKENIZER_PATH}")
    print(f"   设备: {device}")
    
    try:
        serve_engine = HiggsAudioServeEngine(LOCAL_MODEL_PATH, LOCAL_TOKENIZER_PATH, device=device)
        print("✅ 服务引擎初始化成功")
    except Exception as e:
        print(f"❌ 服务引擎初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 运行测试
    success = test_basic_tts_local(serve_engine, output_dir)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 本地模型测试完成！")
        print(f"📁 输出文件保存在: {output_dir.absolute()}")
    else:
        print("❌ 本地模型测试失败")

if __name__ == "__main__":
    main()

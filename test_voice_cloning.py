#!/usr/bin/env python3
"""声音克隆测试脚本"""

import subprocess
import sys
import os
from pathlib import Path

def run_generation_command(cmd, description):
    """运行generation.py命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 40)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ 命令执行成功")
        if result.stdout:
            # 只显示最后几行重要信息
            lines = result.stdout.strip().split('\n')
            important_lines = [line for line in lines[-10:] if 'saved' in line.lower() or 'complete' in line.lower() or 'error' in line.lower()]
            if important_lines:
                print("输出:")
                for line in important_lines:
                    print(f"   {line}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败 (返回码: {e.returncode})")
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ 找不到命令: {cmd[0]}")
        return False

def main():
    """主函数"""
    print("🚀 声音克隆测试")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not os.path.exists("examples/generation.py"):
        print("❌ 错误: 请在higgs-audio项目根目录中运行此脚本")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = Path("voice_cloning_outputs")
    output_dir.mkdir(exist_ok=True)
    
    # 测试用例
    tests = [
        {
            "name": "声音克隆 - Belinda女声",
            "cmd": [
                "bash", "-c", 
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'This is a voice cloning test using Belinda voice. The technology is quite impressive and the results sound very natural.' "
                "--ref_audio belinda "
                "--temperature 0.3 "
                f"--out_path {output_dir / 'voice_clone_belinda.wav'}"
            ]
        },
        {
            "name": "声音克隆 - Broom Salesman男声",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'Hello there! I am testing the voice cloning capability with a different voice. This should sound like the broom salesman character.' "
                "--ref_audio broom_salesman "
                "--temperature 0.3 "
                f"--out_path {output_dir / 'voice_clone_broom_salesman.wav'}"
            ]
        },
        {
            "name": "声音克隆 - Chadwick男声",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'Good evening. This is a demonstration of voice cloning technology using the Chadwick voice profile. The quality should be quite remarkable.' "
                "--ref_audio chadwick "
                "--temperature 0.3 "
                f"--out_path {output_dir / 'voice_clone_chadwick.wav'}"
            ]
        },
        {
            "name": "声音克隆 - 中文男声",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript '大家好，这是使用中文男声进行的声音克隆测试。这项技术非常先进，能够很好地模仿原始声音的特征。' "
                "--ref_audio zh_man_sichuan "
                "--temperature 0.3 "
                f"--out_path {output_dir / 'voice_clone_chinese_male.wav'}"
            ]
        },
        {
            "name": "声音克隆 - 英文女声",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'This is a test using the English woman voice. The voice cloning should preserve the original characteristics while speaking new content.' "
                "--ref_audio en_woman "
                "--temperature 0.3 "
                f"--out_path {output_dir / 'voice_clone_en_woman.wav'}"
            ]
        }
    ]
    
    # 运行测试
    success_count = 0
    for i, test in enumerate(tests, 1):
        print(f"\n🎯 测试 {i}/{len(tests)}: {test['name']}")
        if run_generation_command(test["cmd"], f"运行{test['name']}"):
            success_count += 1
        else:
            print(f"⚠️  测试 {i} 失败，继续下一个测试...")
    
    # 总结
    print("\n" + "=" * 50)
    print(f"🎉 声音克隆测试完成! 成功: {success_count}/{len(tests)}")
    
    if success_count > 0:
        print(f"\n📁 输出文件保存在: {output_dir.absolute()}")
        print("生成的音频文件:")
        for wav_file in sorted(output_dir.glob("*.wav")):
            if wav_file.exists():
                print(f"   🎵 {wav_file.name}")
    
    if success_count == 0:
        print("\n❌ 所有测试都失败了。请检查:")
        print("   1. 是否正确激活了conda环境")
        print("   2. 是否正确安装了所有依赖")
        print("   3. 网络连接是否正常（需要下载模型）")
        print("   4. GPU内存是否足够（建议至少24GB）")

if __name__ == "__main__":
    main()

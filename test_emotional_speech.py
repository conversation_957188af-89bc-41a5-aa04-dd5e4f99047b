#!/usr/bin/env python3
"""情感语音合成测试脚本"""

import subprocess
import sys
import os
from pathlib import Path

def run_generation_command(cmd, description):
    """运行generation.py命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 40)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ 命令执行成功")
        if result.stdout:
            # 只显示最后几行重要信息
            lines = result.stdout.strip().split('\n')
            important_lines = [line for line in lines[-10:] if 'saved' in line.lower() or 'complete' in line.lower() or 'error' in line.lower()]
            if important_lines:
                print("输出:")
                for line in important_lines:
                    print(f"   {line}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败 (返回码: {e.returncode})")
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ 找不到命令: {cmd[0]}")
        return False

def main():
    """主函数"""
    print("🚀 情感语音合成测试")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not os.path.exists("examples/generation.py"):
        print("❌ 错误: 请在higgs-audio项目根目录中运行此脚本")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = Path("emotional_speech_outputs")
    output_dir.mkdir(exist_ok=True)
    
    # 测试用例 - 不同情感的语音
    tests = [
        {
            "name": "兴奋情感",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'I am so excited about this new technology! This is absolutely amazing and incredible!' "
                "--scene_prompt 'Generate speech with excited and enthusiastic emotion.' "
                "--temperature 0.5 "
                f"--out_path {output_dir / 'excited_emotion.wav'}"
            ]
        },
        {
            "name": "悲伤情感",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'I am feeling quite sad today. The weather is gloomy and everything seems difficult.' "
                "--scene_prompt 'Generate speech with sad and melancholic emotion.' "
                "--temperature 0.5 "
                f"--out_path {output_dir / 'sad_emotion.wav'}"
            ]
        },
        {
            "name": "愤怒情感",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'I cannot believe this happened! This is completely unacceptable and frustrating!' "
                "--scene_prompt 'Generate speech with angry and frustrated emotion.' "
                "--temperature 0.5 "
                f"--out_path {output_dir / 'angry_emotion.wav'}"
            ]
        },
        {
            "name": "平静情感",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'Let me explain this calmly and clearly. Everything will be fine, and we can work through this together.' "
                "--scene_prompt 'Generate speech with calm and peaceful emotion.' "
                "--temperature 0.3 "
                f"--out_path {output_dir / 'calm_emotion.wav'}"
            ]
        },
        {
            "name": "神秘情感",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript 'There is something mysterious about this place. I can sense secrets hidden in the shadows.' "
                "--scene_prompt 'Generate speech with mysterious and intriguing emotion.' "
                "--temperature 0.6 "
                f"--out_path {output_dir / 'mysterious_emotion.wav'}"
            ]
        },
        {
            "name": "中文情感 - 温柔",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript '亲爱的，今天天气真好，我们一起去公园走走吧。我会一直陪在你身边的。' "
                "--scene_prompt 'Generate speech with gentle and tender emotion in Chinese.' "
                "--temperature 0.4 "
                f"--out_path {output_dir / 'gentle_chinese.wav'}"
            ]
        }
    ]
    
    # 运行测试
    success_count = 0
    for i, test in enumerate(tests, 1):
        print(f"\n🎯 测试 {i}/{len(tests)}: {test['name']}")
        if run_generation_command(test["cmd"], f"运行{test['name']}"):
            success_count += 1
        else:
            print(f"⚠️  测试 {i} 失败，继续下一个测试...")
    
    # 总结
    print("\n" + "=" * 50)
    print(f"🎉 情感语音合成测试完成! 成功: {success_count}/{len(tests)}")
    
    if success_count > 0:
        print(f"\n📁 输出文件保存在: {output_dir.absolute()}")
        print("生成的音频文件:")
        for wav_file in sorted(output_dir.glob("*.wav")):
            if wav_file.exists():
                print(f"   🎵 {wav_file.name}")

if __name__ == "__main__":
    main()

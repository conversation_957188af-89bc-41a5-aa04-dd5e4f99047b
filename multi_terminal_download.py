#!/usr/bin/env python3
"""使用多个终端并行下载模型文件"""

import subprocess
import time
import os
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

# 模型文件配置
DOWNLOADS = [
    {
        "name": "model-00002-of-00003.safetensors",
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00002-of-00003.safetensors",
        "path": "models/higgs-audio-v2-generation-3B-base/model-00002-of-00003.safetensors",
        "size": "~4.6GB"
    },
    {
        "name": "model-00003-of-00003.safetensors", 
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00003-of-00003.safetensors",
        "path": "models/higgs-audio-v2-generation-3B-base/model-00003-of-00003.safetensors",
        "size": "~1.8GB"
    },
    {
        "name": "tokenizer.json",
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/tokenizer.json",
        "path": "models/higgs-audio-v2-generation-3B-base/tokenizer.json",
        "size": "~2MB"
    },
    {
        "name": "tokenizer_config.json",
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/tokenizer_config.json",
        "path": "models/higgs-audio-v2-generation-3B-base/tokenizer_config.json",
        "size": "~1KB"
    },
    {
        "name": "special_tokens_map.json",
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/special_tokens_map.json",
        "path": "models/higgs-audio-v2-generation-3B-base/special_tokens_map.json",
        "size": "~1KB"
    },
    {
        "name": "audio_tokenizer_config.json",
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-tokenizer/resolve/main/config.json",
        "path": "models/higgs-audio-v2-tokenizer/config.json",
        "size": "~1KB"
    },
    {
        "name": "audio_tokenizer_model.bin",
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-tokenizer/resolve/main/pytorch_model.bin",
        "path": "models/higgs-audio-v2-tokenizer/pytorch_model.bin",
        "size": "~500MB"
    },
    {
        "name": "audio_tokenizer_config2.json",
        "url": "https://hf-mirror.com/bosonai/higgs-audio-v2-tokenizer/resolve/main/tokenizer_config.json",
        "path": "models/higgs-audio-v2-tokenizer/tokenizer_config.json",
        "size": "~1KB"
    }
]

def create_directories():
    """创建必要的目录"""
    Path("models/higgs-audio-v2-generation-3B-base").mkdir(parents=True, exist_ok=True)
    Path("models/higgs-audio-v2-tokenizer").mkdir(parents=True, exist_ok=True)

def download_file(download_info):
    """下载单个文件"""
    name = download_info["name"]
    url = download_info["url"]
    path = download_info["path"]
    size = download_info["size"]
    
    # 检查文件是否已存在
    if os.path.exists(path):
        print(f"⏭️  文件已存在，跳过: {name}")
        return {"name": name, "status": "skipped", "path": path}
    
    print(f"📥 开始下载: {name} ({size})")
    
    try:
        # 使用wget下载文件
        cmd = [
            "wget", 
            "-c",  # 支持断点续传
            "--timeout=30",
            "--tries=3",
            "--progress=bar:force",  # 显示进度条
            url,
            "-O", path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
        
        if result.returncode == 0:
            print(f"✅ 下载完成: {name}")
            return {"name": name, "status": "success", "path": path}
        else:
            print(f"❌ 下载失败: {name}")
            print(f"错误信息: {result.stderr}")
            return {"name": name, "status": "failed", "error": result.stderr}
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 下载超时: {name}")
        return {"name": name, "status": "timeout"}
    except Exception as e:
        print(f"❌ 下载异常: {name} - {e}")
        return {"name": name, "status": "error", "error": str(e)}

def main():
    """主函数"""
    print("🚀 开始多线程并行下载Higgs Audio模型")
    print("=" * 60)
    
    # 创建目录
    create_directories()
    
    # 显示下载计划
    print("📋 下载计划:")
    for i, download in enumerate(DOWNLOADS, 1):
        print(f"   {i}. {download['name']} ({download['size']})")
    
    print(f"\n🔧 使用 {min(len(DOWNLOADS), 4)} 个并行线程下载...")
    print("-" * 60)
    
    # 使用线程池并行下载
    results = []
    with ThreadPoolExecutor(max_workers=4) as executor:
        # 提交所有下载任务
        future_to_download = {
            executor.submit(download_file, download): download 
            for download in DOWNLOADS
        }
        
        # 收集结果
        for future in as_completed(future_to_download):
            download = future_to_download[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"❌ 任务异常: {download['name']} - {e}")
                results.append({
                    "name": download['name'], 
                    "status": "exception", 
                    "error": str(e)
                })
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📊 下载结果统计:")
    
    success_count = sum(1 for r in results if r["status"] == "success")
    skipped_count = sum(1 for r in results if r["status"] == "skipped")
    failed_count = len(results) - success_count - skipped_count
    
    print(f"   ✅ 成功: {success_count}")
    print(f"   ⏭️  跳过: {skipped_count}")
    print(f"   ❌ 失败: {failed_count}")
    
    # 显示详细结果
    print("\n📋 详细结果:")
    for result in results:
        status_icon = {
            "success": "✅",
            "skipped": "⏭️ ",
            "failed": "❌",
            "timeout": "⏰",
            "error": "❌",
            "exception": "❌"
        }.get(result["status"], "❓")
        
        print(f"   {status_icon} {result['name']} - {result['status']}")
        if "error" in result:
            print(f"      错误: {result['error'][:100]}...")
    
    # 检查文件完整性
    print("\n🔍 文件完整性检查:")
    for download in DOWNLOADS:
        path = download["path"]
        if os.path.exists(path):
            size = os.path.getsize(path)
            size_mb = size / (1024 * 1024)
            if size_mb > 1:
                print(f"   ✅ {download['name']}: {size_mb:.1f}MB")
            else:
                print(f"   ✅ {download['name']}: {size}B")
        else:
            print(f"   ❌ {download['name']}: 文件不存在")
    
    if success_count + skipped_count == len(DOWNLOADS):
        print("\n🎉 所有文件下载完成！可以开始测试了。")
    else:
        print(f"\n⚠️  还有 {failed_count} 个文件下载失败，请重新运行脚本。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""基础文本转语音测试脚本"""

import os
import time
import torch
import torchaudio
from pathlib import Path
from boson_multimodal.serve.serve_engine import HiggsAudioServeEngine, HiggsAudioResponse
from boson_multimodal.data_types import ChatMLSample, Message, AudioContent

# 模型配置
MODEL_PATH = "bosonai/higgs-audio-v2-generation-3B-base"
AUDIO_TOKENIZER_PATH = "bosonai/higgs-audio-v2-tokenizer"

def check_gpu():
    """检查GPU可用性"""
    if torch.cuda.is_available():
        print(f"✅ CUDA可用，GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        return "cuda"
    elif torch.backends.mps.is_available():
        print("✅ MPS (Apple Silicon)可用")
        return "mps"
    else:
        print("⚠️  只有CPU可用，生成速度会较慢")
        return "cpu"

def test_basic_english_tts(serve_engine, output_dir):
    """测试基础英文文本转语音"""
    print("\n🎯 测试: 基础英文文本转语音")
    
    system_prompt = (
        "Generate audio following instruction.\n\n<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
    )
    
    messages = [
        Message(role="system", content=system_prompt),
        Message(role="user", content="Hello, this is a test of Higgs Audio text-to-speech synthesis. The weather is nice today, and I hope you're having a wonderful day.")
    ]
    
    start_time = time.time()
    output = serve_engine.generate(
        chat_ml_sample=ChatMLSample(messages=messages),
        max_new_tokens=1024,
        temperature=0.3,
        top_p=0.95,
        top_k=50,
        stop_strings=["<|end_of_text|>", "<|eot_id|>"],
    )
    end_time = time.time()
    
    output_path = output_dir / "basic_english_tts.wav"
    torchaudio.save(str(output_path), torch.from_numpy(output.audio)[None, :], output.sampling_rate)
    
    print(f"✅ 英文TTS测试完成")
    print(f"   生成时间: {end_time - start_time:.2f}秒")
    print(f"   输出文件: {output_path}")
    print(f"   音频长度: {len(output.audio) / output.sampling_rate:.2f}秒")

def test_basic_chinese_tts(serve_engine, output_dir):
    """测试基础中文文本转语音"""
    print("\n🎯 测试: 基础中文文本转语音")
    
    system_prompt = (
        "Generate audio following instruction.\n\n<|scene_desc_start|>\nAudio is recorded from a quiet room.\n<|scene_desc_end|>"
    )
    
    messages = [
        Message(role="system", content=system_prompt),
        Message(role="user", content="你好，这是Higgs Audio中文语音合成测试。今天天气很好，适合出去走走。希望这个测试能够成功运行。")
    ]
    
    start_time = time.time()
    output = serve_engine.generate(
        chat_ml_sample=ChatMLSample(messages=messages),
        max_new_tokens=1024,
        temperature=0.3,
        top_p=0.95,
        top_k=50,
        stop_strings=["<|end_of_text|>", "<|eot_id|>"],
    )
    end_time = time.time()
    
    output_path = output_dir / "basic_chinese_tts.wav"
    torchaudio.save(str(output_path), torch.from_numpy(output.audio)[None, :], output.sampling_rate)
    
    print(f"✅ 中文TTS测试完成")
    print(f"   生成时间: {end_time - start_time:.2f}秒")
    print(f"   输出文件: {output_path}")
    print(f"   音频长度: {len(output.audio) / output.sampling_rate:.2f}秒")

def main():
    """主测试函数"""
    print("🚀 开始基础语音合成测试")
    print("=" * 50)
    
    # 检查GPU
    device = check_gpu()
    
    # 创建输出目录
    output_dir = Path("basic_tts_outputs")
    output_dir.mkdir(exist_ok=True)
    print(f"📁 输出目录: {output_dir.absolute()}")
    
    # 初始化服务引擎
    print(f"\n🔧 初始化Higgs Audio服务引擎...")
    print(f"   模型路径: {MODEL_PATH}")
    print(f"   音频分词器: {AUDIO_TOKENIZER_PATH}")
    print(f"   设备: {device}")
    
    try:
        serve_engine = HiggsAudioServeEngine(MODEL_PATH, AUDIO_TOKENIZER_PATH, device=device)
        print("✅ 服务引擎初始化成功")
    except Exception as e:
        print(f"❌ 服务引擎初始化失败: {e}")
        print("请确保已正确安装依赖并且模型可以正常下载")
        return
    
    # 运行测试
    try:
        test_basic_english_tts(serve_engine, output_dir)
        test_basic_chinese_tts(serve_engine, output_dir)
        
        print("\n" + "=" * 50)
        print("🎉 基础语音合成测试完成！")
        print(f"📁 所有输出文件保存在: {output_dir.absolute()}")
        print("\n生成的音频文件:")
        for wav_file in sorted(output_dir.glob("*.wav")):
            print(f"   🎵 {wav_file.name}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""手动下载Higgs Audio模型文件"""

import os
import requests
import json
from pathlib import Path
from urllib.parse import urljoin

# 模型信息
MODELS = {
    "higgs-audio-v2-generation-3B-base": {
        "repo": "bosonai/higgs-audio-v2-generation-3B-base",
        "files": [
            "config.json",
            "generation_config.json", 
            "model.safetensors.index.json",
            "model-00001-of-00003.safetensors",
            "model-00002-of-00003.safetensors", 
            "model-00003-of-00003.safetensors",
            "tokenizer.json",
            "tokenizer_config.json",
            "special_tokens_map.json"
        ]
    },
    "higgs-audio-v2-tokenizer": {
        "repo": "bosonai/higgs-audio-v2-tokenizer",
        "files": [
            "config.json",
            "pytorch_model.bin",
            "tokenizer_config.json"
        ]
    }
}

def download_file(url, local_path, session=None):
    """下载单个文件"""
    if session is None:
        session = requests.Session()
    
    print(f"下载: {url}")
    print(f"保存到: {local_path}")
    
    try:
        response = session.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        # 创建目录
        local_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 下载文件
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        print(f"✅ 下载完成: {local_path.name}")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def download_model(model_name, model_info, base_dir):
    """下载整个模型"""
    print(f"\n🔧 开始下载模型: {model_name}")
    print("=" * 50)
    
    model_dir = base_dir / model_name
    repo = model_info["repo"]
    
    session = requests.Session()
    # 设置User-Agent
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    success_count = 0
    total_files = len(model_info["files"])
    
    for filename in model_info["files"]:
        # 尝试多个可能的URL
        urls_to_try = [
            f"https://huggingface.co/{repo}/resolve/main/{filename}",
            f"https://hf-mirror.com/{repo}/resolve/main/{filename}",
            f"https://huggingface.co/{repo}/raw/main/{filename}"
        ]
        
        local_path = model_dir / filename
        
        # 如果文件已存在，跳过
        if local_path.exists():
            print(f"⏭️  文件已存在，跳过: {filename}")
            success_count += 1
            continue
        
        downloaded = False
        for url in urls_to_try:
            if download_file(url, local_path, session):
                downloaded = True
                success_count += 1
                break
        
        if not downloaded:
            print(f"❌ 所有URL都失败了: {filename}")
    
    print(f"\n📊 模型 {model_name} 下载完成: {success_count}/{total_files} 文件成功")
    return success_count == total_files

def create_alternative_urls_file():
    """创建备用下载链接文件"""
    urls_content = """# Higgs Audio 模型下载链接

## 主模型 (higgs-audio-v2-generation-3B-base)
如果自动下载失败，可以手动从以下链接下载：

### HuggingFace 官方链接：
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/config.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/generation_config.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model.safetensors.index.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00001-of-00003.safetensors
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00002-of-00003.safetensors
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00003-of-00003.safetensors
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/tokenizer.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/tokenizer_config.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/special_tokens_map.json

### HF Mirror 镜像链接：
- https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/config.json
- https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/generation_config.json
- (其他文件类似，将上面的 huggingface.co 替换为 hf-mirror.com)

## 音频分词器 (higgs-audio-v2-tokenizer)
- https://huggingface.co/bosonai/higgs-audio-v2-tokenizer/resolve/main/config.json
- https://huggingface.co/bosonai/higgs-audio-v2-tokenizer/resolve/main/pytorch_model.bin
- https://huggingface.co/bosonai/higgs-audio-v2-tokenizer/resolve/main/tokenizer_config.json

## 使用方法：
1. 创建目录结构：
   mkdir -p models/higgs-audio-v2-generation-3B-base
   mkdir -p models/higgs-audio-v2-tokenizer

2. 下载文件到对应目录

3. 修改测试脚本中的模型路径为本地路径
"""
    
    with open("model_download_urls.txt", "w", encoding="utf-8") as f:
        f.write(urls_content)
    
    print("✅ 创建了备用下载链接文件: model_download_urls.txt")

def main():
    """主函数"""
    print("🚀 开始下载Higgs Audio模型")
    print("=" * 50)
    
    # 创建模型目录
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # 创建备用链接文件
    create_alternative_urls_file()
    
    # 下载所有模型
    all_success = True
    for model_name, model_info in MODELS.items():
        success = download_model(model_name, model_info, models_dir)
        if not success:
            all_success = False
    
    print("\n" + "=" * 50)
    if all_success:
        print("🎉 所有模型下载完成！")
        print(f"📁 模型保存在: {models_dir.absolute()}")
        print("\n下一步：运行测试脚本时使用本地模型路径")
    else:
        print("⚠️  部分模型下载失败")
        print("请检查网络连接或使用备用下载方式")
        print("详细信息请查看: model_download_urls.txt")

if __name__ == "__main__":
    main()

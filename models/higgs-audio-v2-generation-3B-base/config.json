{"_name_or_path": "model_initialization/higgs_audio_Llama-3.2-3B_dual_ffn_0_1_code8_size1024_rev", "architectures": ["HiggsAudioModel"], "audio_adapter_type": "dual_ffn_fast_forward", "audio_bos_token": "<|audio_bos|>", "audio_codebook_size": 1024, "audio_decoder_proj_num_layers": 0, "audio_dual_ffn_layers": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], "audio_embed_avg": false, "audio_encoder_config": {"_attn_implementation_autoset": false, "_name_or_path": "", "activation_dropout": 0.0, "activation_function": "gelu", "add_cross_attention": false, "architectures": null, "attention_dropout": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "d_model": 1280, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "dropout": 0.0, "early_stopping": false, "encoder_attention_heads": 20, "encoder_ffn_dim": 5120, "encoder_layerdrop": 0.0, "encoder_layers": 32, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "init_std": 0.02, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "length_penalty": 1.0, "max_length": 20, "max_source_positions": 1500, "min_length": 0, "model_type": "higgs_audio_encoder", "no_repeat_ngram_size": 0, "num_beam_groups": 1, "num_beams": 1, "num_hidden_layers": 32, "num_mel_bins": 128, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 128001, "prefix": null, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "scale_embedding": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "typical_p": 1.0, "use_bfloat16": false}, "audio_eos_token": "<|audio_eos|>", "audio_eos_token_id": 128012, "audio_ffn_hidden_size": 3072, "audio_ffn_intermediate_size": 8192, "audio_in_token": "<|AUDIO|>", "audio_in_token_idx": 128015, "audio_num_codebooks": 8, "audio_out_bos_token": "<|audio_out_bos|>", "audio_out_bos_token_id": 128013, "audio_out_token": "<|AUDIO_OUT|>", "audio_out_token_idx": 128016, "audio_stream_bos_id": 1024, "audio_stream_eos_id": 1025, "audio_tokenizer_config": null, "encode_audio_in_tokens": true, "encode_whisper_embed": false, "hidden_size": 3072, "model_type": "higgs_audio", "pad_token_id": 128001, "rq_transformer_hidden_size": null, "rq_transformer_intermediate_size": null, "rq_transformer_num_attention_heads": null, "rq_transformer_num_hidden_layers": 3, "rq_transformer_num_key_value_heads": null, "skip_audio_tower": true, "text_config": {"_attn_implementation_autoset": false, "_name_or_path": "/fsx/models/Llama-3.2-3B", "add_cross_attention": false, "architectures": ["LlamaForCausalLM"], "attention_bias": false, "attention_dropout": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": 128000, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 128001, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "head_dim": 128, "hidden_act": "silu", "hidden_size": 3072, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_range": 0.02, "intermediate_size": 8192, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "length_penalty": 1.0, "max_length": 20, "max_position_embeddings": 131072, "min_length": 0, "mlp_bias": false, "model_type": "llama", "no_repeat_ngram_size": 0, "num_attention_heads": 24, "num_beam_groups": 1, "num_beams": 1, "num_hidden_layers": 28, "num_key_value_heads": 8, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "prefix": null, "pretraining_tp": 1, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "rms_norm_eps": 1e-05, "rope_scaling": {"factor": 32.0, "high_freq_factor": 4.0, "low_freq_factor": 1.0, "original_max_position_embeddings": 8192, "rope_type": "llama3"}, "rope_theta": 500000.0, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": "bfloat16", "torchscript": false, "typical_p": 1.0, "use_bfloat16": false, "use_cache": true, "vocab_size": 128256}, "torch_dtype": "bfloat16", "transformers_version": "4.46.3", "use_audio_out_embed_projector": false, "use_audio_out_self_attention": 0, "use_delay_pattern": true, "use_rq_transformer": false}
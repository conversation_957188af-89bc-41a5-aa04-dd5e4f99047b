# Higgs Audio 模型下载链接

## 主模型 (higgs-audio-v2-generation-3B-base)
如果自动下载失败，可以手动从以下链接下载：

### HuggingFace 官方链接：
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/config.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/generation_config.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model.safetensors.index.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00001-of-00003.safetensors
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00002-of-00003.safetensors
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/model-00003-of-00003.safetensors
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/tokenizer.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/tokenizer_config.json
- https://huggingface.co/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/special_tokens_map.json

### HF Mirror 镜像链接：
- https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/config.json
- https://hf-mirror.com/bosonai/higgs-audio-v2-generation-3B-base/resolve/main/generation_config.json
- (其他文件类似，将上面的 huggingface.co 替换为 hf-mirror.com)

## 音频分词器 (higgs-audio-v2-tokenizer)
- https://huggingface.co/bosonai/higgs-audio-v2-tokenizer/resolve/main/config.json
- https://huggingface.co/bosonai/higgs-audio-v2-tokenizer/resolve/main/pytorch_model.bin
- https://huggingface.co/bosonai/higgs-audio-v2-tokenizer/resolve/main/tokenizer_config.json

## 使用方法：
1. 创建目录结构：
   mkdir -p models/higgs-audio-v2-generation-3B-base
   mkdir -p models/higgs-audio-v2-tokenizer

2. 下载文件到对应目录

3. 修改测试脚本中的模型路径为本地路径

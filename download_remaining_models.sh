#!/bin/bash

# 下载剩余的模型文件
echo "🚀 下载剩余的Higgs Audio模型文件"
echo "=" * 50

# 创建目录
mkdir -p models/higgs-audio-v2-generation-3B-base
mkdir -p models/higgs-audio-v2-tokenizer

# 基础URL
BASE_URL="https://hf-mirror.com"
MODEL_REPO="bosonai/higgs-audio-v2-generation-3B-base"
TOKENIZER_REPO="bosonai/higgs-audio-v2-tokenizer"

# 下载主模型剩余文件
echo "📥 下载主模型剩余文件..."
cd models/higgs-audio-v2-generation-3B-base

# 检查并下载缺失的文件
files_to_download=(
    "model-00001-of-00003.safetensors"
    "model-00003-of-00003.safetensors"
    "tokenizer.json"
    "tokenizer_config.json"
    "special_tokens_map.json"
)

for file in "${files_to_download[@]}"; do
    if [ ! -f "$file" ]; then
        echo "下载: $file"
        wget -c --timeout=30 --tries=3 "${BASE_URL}/${MODEL_REPO}/resolve/main/${file}" -O "$file"
        if [ $? -eq 0 ]; then
            echo "✅ 下载完成: $file"
        else
            echo "❌ 下载失败: $file"
        fi
    else
        echo "⏭️  文件已存在: $file"
    fi
done

cd ../..

# 下载音频分词器
echo "📥 下载音频分词器..."
cd models/higgs-audio-v2-tokenizer

tokenizer_files=(
    "config.json"
    "pytorch_model.bin"
    "tokenizer_config.json"
)

for file in "${tokenizer_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "下载: $file"
        wget -c --timeout=30 --tries=3 "${BASE_URL}/${TOKENIZER_REPO}/resolve/main/${file}" -O "$file"
        if [ $? -eq 0 ]; then
            echo "✅ 下载完成: $file"
        else
            echo "❌ 下载失败: $file"
        fi
    else
        echo "⏭️  文件已存在: $file"
    fi
done

cd ../..

echo "🎉 模型下载完成！"
echo "📁 模型文件位置:"
echo "   - 主模型: models/higgs-audio-v2-generation-3B-base/"
echo "   - 分词器: models/higgs-audio-v2-tokenizer/"

#!/usr/bin/env python3
"""多说话人对话测试脚本"""

import subprocess
import sys
import os
from pathlib import Path

def run_generation_command(cmd, description):
    """运行generation.py命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 40)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("✅ 命令执行成功")
        if result.stdout:
            # 只显示最后几行重要信息
            lines = result.stdout.strip().split('\n')
            important_lines = [line for line in lines[-10:] if 'saved' in line.lower() or 'complete' in line.lower() or 'error' in line.lower()]
            if important_lines:
                print("输出:")
                for line in important_lines:
                    print(f"   {line}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败 (返回码: {e.returncode})")
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False
    except FileNotFoundError:
        print(f"❌ 找不到命令: {cmd[0]}")
        return False

def main():
    """主函数"""
    print("🚀 多说话人对话测试")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not os.path.exists("examples/generation.py"):
        print("❌ 错误: 请在higgs-audio项目根目录中运行此脚本")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = Path("multi_speaker_outputs")
    output_dir.mkdir(exist_ok=True)
    
    # 测试用例
    tests = [
        {
            "name": "多说话人对话 - 自动分配声音",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript examples/transcript/multi_speaker/en_argument.txt "
                "--seed 12345 "
                f"--out_path {output_dir / 'multi_speaker_auto.wav'}"
            ]
        },
        {
            "name": "多说话人对话 - 指定声音克隆",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript examples/transcript/multi_speaker/en_argument.txt "
                "--ref_audio belinda,broom_salesman "
                "--ref_audio_in_system_message "
                "--chunk_method speaker "
                "--seed 12345 "
                f"--out_path {output_dir / 'multi_speaker_cloned.wav'}"
            ]
        },
        {
            "name": "自定义多说话人对话",
            "cmd": [
                "bash", "-c",
                "source activate ./conda_env && python3 examples/generation.py "
                "--transcript '[SPEAKER0] Hello there! How are you doing today? [SPEAKER1] I am doing great, thank you for asking! How about you? [SPEAKER0] I am wonderful! I wanted to talk to you about this new AI technology. [SPEAKER1] Oh, that sounds interesting! Tell me more about it.' "
                "--seed 42 "
                f"--out_path {output_dir / 'custom_dialogue.wav'}"
            ]
        }
    ]
    
    # 运行测试
    success_count = 0
    for i, test in enumerate(tests, 1):
        print(f"\n🎯 测试 {i}/{len(tests)}: {test['name']}")
        if run_generation_command(test["cmd"], f"运行{test['name']}"):
            success_count += 1
        else:
            print(f"⚠️  测试 {i} 失败，继续下一个测试...")
    
    # 总结
    print("\n" + "=" * 50)
    print(f"🎉 多说话人对话测试完成! 成功: {success_count}/{len(tests)}")
    
    if success_count > 0:
        print(f"\n📁 输出文件保存在: {output_dir.absolute()}")
        print("生成的音频文件:")
        for wav_file in sorted(output_dir.glob("*.wav")):
            if wav_file.exists():
                print(f"   🎵 {wav_file.name}")

if __name__ == "__main__":
    main()
